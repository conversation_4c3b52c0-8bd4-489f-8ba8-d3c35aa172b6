// 缓存键名
const CACHE_KEY = 'yqh_selected_activity_id';

// 从 localStorage 获取缓存的活动ID
function getCachedActivityId() {
  try {
    const cached = localStorage.getItem(CACHE_KEY);
    console.log('读取缓存的活动ID:', cached);
    return cached;
  } catch (error) {
    console.warn('无法读取缓存的活动ID:', error);
    return null;
  }
}

// 缓存活动ID到 localStorage
function setCachedActivityId(activityId) {
  try {
    if (activityId) {
      console.log('缓存活动ID:', activityId);
      localStorage.setItem(CACHE_KEY, activityId.toString());
    } else {
      console.log('清除缓存的活动ID');
      localStorage.removeItem(CACHE_KEY);
    }
  } catch (error) {
    console.warn('无法缓存活动ID:', error);
  }
}

export default {
  namespaced: true,
  state: {
    // 用户活动列表
    userActivities: [],
    // 当前选中的活动ID
    selectedActivityId: null,
    // 当前活动对象
    currentActivity: null,
    // 活动选项（用于下拉菜单）
    activityOptions: [],
    // 是否正在加载活动列表
    loading: false,
    // 活动过期状态列表
    expirationStatusList: [],
    // 续费套餐列表
    renewalPackages: []
  },
  
  getters: {
    // 获取当前活动名称
    currentActivityName: (state) => {
      return state.currentActivity ? state.currentActivity.name : '选择活动';
    },
    
    // 检查是否有活动
    hasActivities: (state) => {
      return state.userActivities && state.userActivities.length > 0;
    },
    
    // 获取活动数量
    activityCount: (state) => {
      return state.userActivities ? state.userActivities.length : 0;
    },

    // 获取当前活动的过期状态
    currentActivityExpirationStatus: (state) => {
      if (!state.selectedActivityId || !state.expirationStatusList) return null;
      return state.expirationStatusList.find(status => status.activityId === state.selectedActivityId);
    },

    // 检查当前活动是否过期
    isCurrentActivityExpired: (state, getters) => {
      const status = getters.currentActivityExpirationStatus;
      return status ? status.isExpired : false;
    },

    // 检查当前活动是否即将过期
    isCurrentActivityExpiringSoon: (state, getters) => {
      const status = getters.currentActivityExpirationStatus;
      return status ? status.isExpiringSoon : false;
    },

    // 获取过期活动数量
    expiredActivityCount: (state) => {
      return state.expirationStatusList.filter(status => status.isExpired).length;
    },

    // 获取即将过期活动数量
    expiringSoonActivityCount: (state) => {
      return state.expirationStatusList.filter(status => status.isExpiringSoon).length;
    }
  },
  
  mutations: {
    // 设置用户活动列表
    SET_USER_ACTIVITIES(state, activities) {
      state.userActivities = activities || [];
      // 同时更新活动选项
      state.activityOptions = activities.map(activity => ({
        text: activity.name,
        value: activity.id
      }));
    },
    
    // 设置当前选中的活动
    SET_SELECTED_ACTIVITY(state, { activityId, activity }) {
      console.log('=== SET_SELECTED_ACTIVITY ===');
      console.log('设置活动ID:', activityId);
      console.log('活动对象:', activity ? activity.name : '无');

      state.selectedActivityId = activityId;

      // 缓存选中的活动ID
      setCachedActivityId(activityId);

      // 如果传入了activity对象，直接使用；否则从列表中查找
      if (activity) {
        state.currentActivity = activity;
      } else if (activityId) {
        const foundActivity = state.userActivities.find(a => a.id === activityId);
        state.currentActivity = foundActivity || null;
      } else {
        state.currentActivity = null;
      }

      console.log('最终设置的活动:', state.currentActivity ? state.currentActivity.name : '无');
      console.log('=== SET_SELECTED_ACTIVITY 完成 ===');
    },
    
    // 设置加载状态
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    
    // 清空活动状态
    CLEAR_ACTIVITIES(state) {
      state.userActivities = [];
      state.selectedActivityId = null;
      state.currentActivity = null;
      state.activityOptions = [];
      // 清除缓存
      setCachedActivityId(null);
    },
    
    // 添加单个活动
    ADD_ACTIVITY(state, activity) {
      if (!state.userActivities.find(a => a.id === activity.id)) {
        state.userActivities.push(activity);
        state.activityOptions.push({
          text: activity.name,
          value: activity.id
        });
      }
    },
    
    // 移除单个活动
    REMOVE_ACTIVITY(state, activityId) {
      state.userActivities = state.userActivities.filter(a => a.id !== activityId);
      state.activityOptions = state.activityOptions.filter(o => o.value !== activityId);

      // 如果移除的是当前选中的活动，清空当前活动和缓存
      if (state.selectedActivityId === activityId) {
        state.selectedActivityId = null;
        state.currentActivity = null;
        setCachedActivityId(null);
      }
    },

    // 设置过期状态列表
    SET_EXPIRATION_STATUS_LIST(state, statusList) {
      state.expirationStatusList = statusList || [];
    },

    // 设置续费套餐列表
    SET_RENEWAL_PACKAGES(state, packages) {
      state.renewalPackages = packages || [];
    },

    // 更新单个活动的过期状态
    UPDATE_ACTIVITY_EXPIRATION_STATUS(state, { activityId, status }) {
      const index = state.expirationStatusList.findIndex(s => s.activityId === activityId);
      if (index !== -1) {
        state.expirationStatusList.splice(index, 1, status);
      } else {
        state.expirationStatusList.push(status);
      }
    }
  },
  
  actions: {
    // 获取用户活动列表
    async fetchUserActivities({ commit, state }, { api, toast }) {
      commit('SET_LOADING', true);

      try {
        const res = await api.get("/pyp/web/activity/userActivities");

        if (res.code === 200) {
          const activities = res.data || [];
          commit('SET_USER_ACTIVITIES', activities);

          console.log('=== fetchUserActivities 处理活动选择 ===');
          console.log('获取到的活动数量:', activities.length);
          console.log('当前 selectedActivityId:', state.selectedActivityId);
          console.log('缓存的活动ID:', getCachedActivityId());

          // 获取活动列表后，优先使用缓存的活动ID
          if (activities.length > 0) {
            const cachedActivityId = getCachedActivityId();
            let selectedActivity = null;

            // 优先使用缓存的活动
            if (cachedActivityId) {
              selectedActivity = activities.find(a => a.id.toString() === cachedActivityId);
              if (selectedActivity) {
                console.log('找到缓存的活动，使用:', selectedActivity.name);
              }
            }

            // 如果缓存的活动不存在，使用第一个活动
            if (!selectedActivity) {
              selectedActivity = activities[0];
              console.log('缓存的活动不存在，使用第一个活动:', selectedActivity.name);
            }

            commit('SET_SELECTED_ACTIVITY', {
              activityId: selectedActivity.id,
              activity: selectedActivity
            });
          }
          console.log('=== fetchUserActivities 处理完成 ===');

          return { success: true, data: activities };
        } else {
          if (toast) {
            toast.fail(res.msg || '获取活动列表失败');
          }
          return { success: false, error: res.msg };
        }
      } catch (error) {
        console.error('获取活动列表失败:', error);
        if (toast) {
          toast.fail('获取活动列表失败');
        }
        return { success: false, error: error.message };
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    // 切换活动
    switchActivity({ commit, state }, activityId) {
      const activity = state.userActivities.find(a => a.id === activityId);
      if (activity) {
        commit('SET_SELECTED_ACTIVITY', {
          activityId,
          activity
        });
        return { success: true, activity };
      } else {
        console.error('Activity not found:', activityId);
        return { success: false, error: 'Activity not found' };
      }
    },
    
    // 初始化活动状态（从localStorage恢复或获取默认）
    async initializeActivity({ commit, dispatch, state }, { api, toast, preferredActivityId = null }) {
      console.log('=== initializeActivity 开始 ===');
      console.log('preferredActivityId:', preferredActivityId);
      console.log('当前 selectedActivityId:', state.selectedActivityId);
      console.log('缓存的活动ID:', getCachedActivityId());
      console.log('已有活动列表长度:', state.userActivities.length);

      // 获取活动列表（如果还没有的话）
      let result;
      if (state.userActivities.length === 0) {
        console.log('没有活动列表，获取活动列表');
        result = await dispatch('fetchUserActivities', { api, toast });
        if (!result.success) {
          return result;
        }
      } else {
        console.log('已有活动列表');
        result = { success: true, data: state.userActivities };
      }

      // 如果指定了首选活动ID，切换到该活动
      if (preferredActivityId && result.data.length > 0) {
        console.log('尝试切换到首选活动:', preferredActivityId);
        const switchResult = await dispatch('switchActivity', preferredActivityId);
        if (!switchResult.success) {
          console.warn('首选活动不存在，保持当前选择');
        }
      }

      console.log('=== initializeActivity 结束 ===');
      console.log('最终选中的活动:', state.currentActivity ? state.currentActivity.name : '无');
      return result;
    },

    // 获取用户活动过期状态
    fetchUserActivitiesExpirationStatus({ commit }, { api }) {
      return api.get('/pyp/web/activity/expiration/userActivitiesStatus')
        .then((res) => {
          if (res.code === 200) {
            commit('SET_EXPIRATION_STATUS_LIST', res.statusList);
            return res.statusList;
          } else {
            throw new Error(res.msg || '获取过期状态失败');
          }
        })
        .catch((error) => {
          console.error('获取活动过期状态失败:', error);
          throw error;
        });
    },

    // 获取单个活动过期状态
    fetchActivityExpirationStatus({ commit }, { activityId, api }) {
      return api.get(`/pyp/web/activity/expiration/status/${activityId}`)
        .then((res) => {
          if (res.code === 200) {
            commit('UPDATE_ACTIVITY_EXPIRATION_STATUS', {
              activityId,
              status: res.status
            });
            return res.status;
          } else {
            throw new Error(res.msg || '获取过期状态失败');
          }
        })
        .catch((error) => {
          console.error('获取活动过期状态失败:', error);
          throw error;
        });
    },

    // 获取续费套餐列表
    fetchRenewalPackages({ commit }, { api }) {
      return api.get('/pyp/web/activity/expiration/renewalPackages')
        .then((res) => {
          if (res.code === 200) {
            commit('SET_RENEWAL_PACKAGES', res.packages);
            return res.packages;
          } else {
            throw new Error(res.msg || '获取续费套餐失败');
          }
        })
        .catch((error) => {
          console.error('获取续费套餐失败:', error);
          throw error;
        });
    },

    // 创建续费订单
    createRenewalOrder({ dispatch }, { orderData, api }) {
      return api.post('/pyp/web/activity/expiration/createRenewalOrder', orderData)
        .then((res) => {
          if (res.code === 200) {
            // 创建成功后刷新过期状态
            return dispatch('fetchActivityExpirationStatus', {
              activityId: orderData.activityId,
              api
            }).then(() => res);
          } else {
            throw new Error(res.msg || '创建续费订单失败');
          }
        })
        .catch((error) => {
          console.error('创建续费订单失败:', error);
          throw error;
        });
    }
  }
};
